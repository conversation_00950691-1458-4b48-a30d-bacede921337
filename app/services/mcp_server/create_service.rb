# frozen_string_literal: true

class McpServer::CreateService
  def initialize(seller:, params:)
    @seller = seller
    @params = params
  end

  def process
    ActiveRecord::Base.transaction do
      create_link
      create_mcp_server_config
      
      { success: true, mcp_server_id: @link.unique_permalink }
    rescue ActiveRecord::RecordInvalid => e
      { success: false, errors: e.record.errors.full_messages }
    rescue StandardError => e
      { success: false, errors: [e.message] }
    end
  end

  private

  attr_reader :seller, :params

  def create_link
    @link = seller.links.build(link_params)
    @link.native_type = Link::NATIVE_TYPE_MCP_SERVER
    @link.filetype = "mcp_server"
    @link.filegroup = "service"
    @link.save!
  end

  def create_mcp_server_config
    config_params = {
      endpoint_url: params[:endpoint_url],
      description: params[:description],
      api_documentation: params[:api_documentation],
      supported_tools: params[:supported_tools] || [],
      server_metadata: params[:server_metadata] || {}
    }
    
    @link.create_mcp_server_config!(config_params)
  end

  def link_params
    {
      name: params[:name],
      description: params[:description],
      price_cents: params[:price_cents] || 0,
      draft: true
    }
  end
end
