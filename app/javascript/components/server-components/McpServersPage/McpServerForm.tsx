import * as React from "react";
import { useLoaderData, useNavigate, useParams } from "react-router-dom";

import { createMcpServer, updateMcpServer } from "$app/data/mcp_servers";
import { asyncVoid } from "$app/utils/promise";
import { assertResponseError } from "$app/utils/request";

import { Button } from "$app/components/Button";
import { Icon } from "$app/components/Icons";
import { showAlert } from "$app/components/server-components/Alert";
import { Layout } from "$app/components/server-components/McpServersPage";

type McpServerFormData = {
  name: string;
  description: string;
  endpoint_url: string;
  price_cents: number;
  api_documentation: string;
  supported_tools: string[];
  server_metadata: Record<string, any>;
};

type McpServerFormResponse = {
  mcp_server?: McpServerFormData & { id: string };
  errors?: string[];
};

const McpServerForm = () => {
  const data = useLoaderData() as McpServerFormResponse;
  const navigate = useNavigate();
  const params = useParams();
  const isEditing = !!params.id;

  const [formData, setFormData] = React.useState<McpServerFormData>({
    name: data.mcp_server?.name || "",
    description: data.mcp_server?.description || "",
    endpoint_url: data.mcp_server?.endpoint_url || "",
    price_cents: data.mcp_server?.price_cents || 0,
    api_documentation: data.mcp_server?.api_documentation || "",
    supported_tools: data.mcp_server?.supported_tools || [],
    server_metadata: data.mcp_server?.server_metadata || {},
  });

  const [toolsInput, setToolsInput] = React.useState(
    formData.supported_tools.join(", ")
  );

  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [errors, setErrors] = React.useState<string[]>([]);

  const handleSubmit = asyncVoid(async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors([]);

    try {
      const submitData = {
        ...formData,
        supported_tools: toolsInput
          .split(",")
          .map(tool => tool.trim())
          .filter(tool => tool.length > 0),
      };

      const response = isEditing
        ? await updateMcpServer(params.id!, submitData)
        : await createMcpServer(submitData);

      if (response.success) {
        showAlert(
          isEditing ? "MCP server updated successfully!" : "MCP server created successfully!",
          "success"
        );
        navigate("/mcp_servers");
      } else {
        setErrors(response.errors || ["An error occurred"]);
      }
    } catch (error) {
      assertResponseError(error);
      setErrors([error.message]);
    } finally {
      setIsSubmitting(false);
    }
  });

  const handleInputChange = (field: keyof McpServerFormData) => (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: field === "price_cents" ? parseInt(e.target.value) || 0 : e.target.value,
    }));
  };

  return (
    <Layout 
      title={isEditing ? "Edit MCP Server" : "Create MCP Server"}
      actions={
        <Button onClick={() => navigate("/mcp_servers")}>
          <Icon name="arrow-left" />
          Back to MCP Servers
        </Button>
      }
    >
      <form onSubmit={handleSubmit} className="form">
        {errors.length > 0 && (
          <div className="alert error">
            <ul>
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        <div className="form-group">
          <label htmlFor="name">Server Name *</label>
          <input
            id="name"
            type="text"
            value={formData.name}
            onChange={handleInputChange("name")}
            placeholder="My Awesome MCP Server"
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="description">Description</label>
          <textarea
            id="description"
            value={formData.description}
            onChange={handleInputChange("description")}
            placeholder="Describe what your MCP server does..."
            rows={4}
          />
        </div>

        <div className="form-group">
          <label htmlFor="endpoint_url">Endpoint URL *</label>
          <input
            id="endpoint_url"
            type="url"
            value={formData.endpoint_url}
            onChange={handleInputChange("endpoint_url")}
            placeholder="https://api.example.com/mcp"
            required
          />
          <small>The URL where your MCP server is hosted</small>
        </div>

        <div className="form-group">
          <label htmlFor="price_cents">Price (in cents)</label>
          <input
            id="price_cents"
            type="number"
            value={formData.price_cents}
            onChange={handleInputChange("price_cents")}
            placeholder="0"
            min="0"
          />
          <small>Price in cents (e.g., 500 = $5.00). Set to 0 for free.</small>
        </div>

        <div className="form-group">
          <label htmlFor="supported_tools">Supported Tools</label>
          <input
            id="supported_tools"
            type="text"
            value={toolsInput}
            onChange={(e) => setToolsInput(e.target.value)}
            placeholder="search, analyze, generate, translate"
          />
          <small>Comma-separated list of tools your server supports</small>
        </div>

        <div className="form-group">
          <label htmlFor="api_documentation">API Documentation</label>
          <textarea
            id="api_documentation"
            value={formData.api_documentation}
            onChange={handleInputChange("api_documentation")}
            placeholder="Provide documentation for your MCP server API..."
            rows={6}
          />
        </div>

        <div className="form-actions">
          <Button type="submit" color="accent" disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : isEditing ? "Update Server" : "Create Server"}
          </Button>
          <Button type="button" onClick={() => navigate("/mcp_servers")}>
            Cancel
          </Button>
        </div>
      </form>
    </Layout>
  );
};

export default McpServerForm;
