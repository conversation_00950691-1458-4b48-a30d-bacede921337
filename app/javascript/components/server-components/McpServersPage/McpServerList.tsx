import * as React from "react";
import { <PERSON>, useLoaderData } from "react-router-dom";

import { formatPriceCentsWithCurrencySymbol, CurrencyCode } from "$app/utils/currency";
import { formatDate } from "$app/utils/date";

import { Icon } from "$app/components/Icons";
import {
  Layout,
  NewMcpServerButton,
  EditMcpServerButton,
  McpServerHealthBadge,
  EmptyStatePlaceholder,
} from "$app/components/server-components/McpServersPage";

export type McpServer = {
  id: string;
  name: string;
  description: string;
  endpoint_url: string;
  price_cents: number;
  currency_code: string;
  health_status: "healthy" | "unhealthy" | "unknown";
  last_health_check_at: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  supported_tools: string[];
  total_requests: number;
  successful_requests: number;
  failed_requests: number;
  average_response_time_ms: number;
  published: boolean;
};

type McpServersResponse = {
  mcp_servers: McpServer[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_count: number;
  };
};

const McpServerRow = ({ mcpServer }: { mcpServer: McpServer }) => {
  const successRate =
    mcpServer.total_requests > 0 ? ((mcpServer.successful_requests / mcpServer.total_requests) * 100).toFixed(1) : "0";

  return (
    <tr>
      <td>
        <div className="product-info">
          <Link to={`/mcp_servers/${mcpServer.id}`} className="product-name">
            {mcpServer.name}
          </Link>
          <div className="product-meta">
            <span className="endpoint">{mcpServer.endpoint_url}</span>
            {mcpServer.supported_tools.length > 0 && (
              <span className="tools-count">
                {mcpServer.supported_tools.length} tool{mcpServer.supported_tools.length !== 1 ? "s" : ""}
              </span>
            )}
          </div>
        </div>
      </td>
      <td>
        <span className="price">
          {formatPriceCentsWithCurrencySymbol(mcpServer.currency_code as CurrencyCode, mcpServer.price_cents, {
            symbolFormat: "short",
          })}
        </span>
      </td>
      <td>
        <McpServerHealthBadge status={mcpServer.health_status} />
        {mcpServer.last_health_check_at && (
          <div className="health-meta">Last checked: {formatDate(new Date(mcpServer.last_health_check_at))}</div>
        )}
      </td>
      <td>
        <div className="stats">
          <div className="stat">
            <span className="stat-value">{mcpServer.total_requests}</span>
            <span className="stat-label">Requests</span>
          </div>
          <div className="stat">
            <span className="stat-value">{successRate}%</span>
            <span className="stat-label">Success Rate</span>
          </div>
          {mcpServer.average_response_time_ms > 0 && (
            <div className="stat">
              <span className="stat-value">{mcpServer.average_response_time_ms.toFixed(0)}ms</span>
              <span className="stat-label">Avg Response</span>
            </div>
          )}
        </div>
      </td>
      <td>
        <div className="status-badges">
          {mcpServer.published ? (
            <span className="badge success">Published</span>
          ) : (
            <span className="badge warning">Draft</span>
          )}
          {mcpServer.is_active ? (
            <span className="badge success">Active</span>
          ) : (
            <span className="badge danger">Inactive</span>
          )}
        </div>
      </td>
      <td>
        <div className="actions">
          <EditMcpServerButton id={mcpServer.id} />
          <Link to={`/mcp_servers/${mcpServer.id}`} className="button">
            <Icon name="eye-fill" />
            View
          </Link>
        </div>
      </td>
    </tr>
  );
};

const McpServerList = () => {
  const data = useLoaderData() as McpServersResponse;
  const { mcp_servers: mcpServers } = data;

  if (mcpServers.length === 0) {
    return (
      <Layout title="MCP Servers">
        <EmptyStatePlaceholder />
      </Layout>
    );
  }

  return (
    <Layout title="MCP Servers" actions={<NewMcpServerButton />}>
      <div className="table-container">
        <table>
          <thead>
            <tr>
              <th>Server</th>
              <th>Price</th>
              <th>Health</th>
              <th>Usage Stats</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {mcpServers.map((mcpServer) => (
              <McpServerRow key={mcpServer.id} mcpServer={mcpServer} />
            ))}
          </tbody>
        </table>
      </div>
    </Layout>
  );
};

export default McpServerList;
