import * as React from "react";
import { useLoaderData, useNavigate } from "react-router-dom";

import { formatPriceCentsWithCurrencySymbol, CurrencyCode } from "$app/utils/currency";
import { formatDate } from "$app/utils/date";

import { Button } from "$app/components/Button";
import { Icon } from "$app/components/Icons";
import { Layout, EditMcpServerButton, McpServerHealthBadge } from "$app/components/server-components/McpServersPage";

type McpServerDetail = {
  id: string;
  name: string;
  description: string;
  endpoint_url: string;
  price_cents: number;
  currency_code: string;
  health_status: "healthy" | "unhealthy" | "unknown";
  last_health_check_at: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  supported_tools: string[];
  api_documentation: string;
  server_metadata: Record<string, any>;
  usage_stats: {
    total_requests: number;
    successful_requests: number;
    failed_requests: number;
    average_response_time_ms: number;
    success_rate: number;
  };
  published: boolean;
};

type McpServerDetailResponse = {
  mcp_server: McpServerDetail;
};

const McpServerDetail = () => {
  const data = useLoaderData() as McpServerDetailResponse;
  const navigate = useNavigate();
  const { mcp_server: server } = data;

  return (
    <Layout
      title={server.name}
      actions={
        <div className="actions-group">
          <EditMcpServerButton id={server.id} />
          <Button onClick={() => navigate("/mcp_servers")}>
            <Icon name="arrow-left" />
            Back to MCP Servers
          </Button>
        </div>
      }
    >
      <div className="server-detail">
        <div className="server-overview">
          <div className="overview-section">
            <h2>Overview</h2>
            <div className="overview-grid">
              <div className="overview-item">
                <label>Status</label>
                <div className="status-badges">
                  {server.published ? (
                    <span className="badge success">Published</span>
                  ) : (
                    <span className="badge warning">Draft</span>
                  )}
                  {server.is_active ? (
                    <span className="badge success">Active</span>
                  ) : (
                    <span className="badge danger">Inactive</span>
                  )}
                </div>
              </div>

              <div className="overview-item">
                <label>Price</label>
                <span className="price">
                  {formatPriceCentsWithCurrencySymbol(server.currency_code as CurrencyCode, server.price_cents, {
                    symbolFormat: "short",
                  })}
                </span>
              </div>

              <div className="overview-item">
                <label>Health Status</label>
                <div>
                  <McpServerHealthBadge status={server.health_status} />
                  {server.last_health_check_at && (
                    <div className="health-meta">Last checked: {formatDate(new Date(server.last_health_check_at))}</div>
                  )}
                </div>
              </div>

              <div className="overview-item">
                <label>Endpoint URL</label>
                <a href={server.endpoint_url} target="_blank" rel="noopener noreferrer" className="endpoint-link">
                  {server.endpoint_url}
                  <Icon name="arrow-diagonal-up-right" />
                </a>
              </div>
            </div>
          </div>

          {server.description && (
            <div className="overview-section">
              <h3>Description</h3>
              <p className="description">{server.description}</p>
            </div>
          )}

          {server.supported_tools.length > 0 && (
            <div className="overview-section">
              <h3>Supported Tools</h3>
              <div className="tools-list">
                {server.supported_tools.map((tool, index) => (
                  <span key={index} className="tool-badge">
                    {tool}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="server-stats">
          <h2>Usage Statistics</h2>
          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-value">{server.usage_stats.total_requests}</div>
              <div className="stat-label">Total Requests</div>
            </div>

            <div className="stat-card">
              <div className="stat-value">{server.usage_stats.successful_requests}</div>
              <div className="stat-label">Successful</div>
            </div>

            <div className="stat-card">
              <div className="stat-value">{server.usage_stats.failed_requests}</div>
              <div className="stat-label">Failed</div>
            </div>

            <div className="stat-card">
              <div className="stat-value">{server.usage_stats.success_rate.toFixed(1)}%</div>
              <div className="stat-label">Success Rate</div>
            </div>

            {server.usage_stats.average_response_time_ms > 0 && (
              <div className="stat-card">
                <div className="stat-value">{server.usage_stats.average_response_time_ms.toFixed(0)}ms</div>
                <div className="stat-label">Avg Response Time</div>
              </div>
            )}
          </div>
        </div>

        {server.api_documentation && (
          <div className="server-documentation">
            <h2>API Documentation</h2>
            <div className="documentation-content">
              <pre>{server.api_documentation}</pre>
            </div>
          </div>
        )}

        <div className="server-metadata">
          <h2>Server Information</h2>
          <div className="metadata-grid">
            <div className="metadata-item">
              <label>Created</label>
              <span>{formatDate(new Date(server.created_at))}</span>
            </div>

            <div className="metadata-item">
              <label>Last Updated</label>
              <span>{formatDate(new Date(server.updated_at))}</span>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default McpServerDetail;
