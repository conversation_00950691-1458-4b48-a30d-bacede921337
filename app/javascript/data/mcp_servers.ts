import { request } from "$app/utils/request";

export type McpServerFormData = {
  name: string;
  description: string;
  endpoint_url: string;
  price_cents: number;
  api_documentation: string;
  supported_tools: string[];
  server_metadata: Record<string, any>;
};

export type McpServer = McpServerFormData & {
  id: string;
  currency_code: string;
  health_status: "healthy" | "unhealthy" | "unknown";
  last_health_check_at: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  total_requests: number;
  successful_requests: number;
  failed_requests: number;
  average_response_time_ms: number;
  published: boolean;
};

export type McpServersResponse = {
  mcp_servers: McpServer[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_count: number;
  };
};

export type McpServerDetailResponse = {
  mcp_server: McpServer & {
    usage_stats: {
      total_requests: number;
      successful_requests: number;
      failed_requests: number;
      average_response_time_ms: number;
      success_rate: number;
    };
  };
};

export type McpServerFormResponse = {
  mcp_server?: McpServer;
  errors?: string[];
};

export type McpServerCreateUpdateResponse = {
  success: boolean;
  mcp_server_id?: string;
  errors?: string[];
};

export const getMcpServers = async (params: { page?: number; query?: string } = {}) => {
  const searchParams = new URLSearchParams();
  if (params.page) searchParams.set("page", params.page.toString());
  if (params.query) searchParams.set("query", params.query);

  const url = `/api/internal/mcp_servers${searchParams.toString() ? `?${searchParams}` : ""}`;
  const response = await request({ url, method: "GET", accept: "json" });

  if (!response.ok) {
    throw new Error("Failed to fetch MCP servers");
  }

  return response.json() as Promise<McpServersResponse>;
};

export const getNewMcpServer = async () => {
  const response = await request({ url: "/api/internal/mcp_servers/new", method: "GET", accept: "json" });

  if (!response.ok) {
    throw new Error("Failed to get new MCP server form");
  }

  return response.json() as Promise<McpServerFormResponse>;
};

export const getEditMcpServer = async (id: string) => {
  const response = await request({ url: `/api/internal/mcp_servers/${id}/edit`, method: "GET", accept: "json" });

  if (!response.ok) {
    return { success: false, status: response.status };
  }

  const data = (await response.json()) as McpServerFormResponse;
  return { success: true, ...data };
};

export const getMcpServerDetail = async (id: string) => {
  const response = await request({ url: `/api/internal/mcp_servers/${id}`, method: "GET", accept: "json" });

  if (!response.ok) {
    return { success: false, status: response.status };
  }

  const data = (await response.json()) as McpServerDetailResponse;
  return { success: true, ...data };
};

export const createMcpServer = async (data: McpServerFormData) => {
  const response = await request({
    url: "/api/internal/mcp_servers",
    method: "POST",
    accept: "json",
    data: { mcp_server: data },
  });

  return response.json() as Promise<McpServerCreateUpdateResponse>;
};

export const updateMcpServer = async (id: string, data: McpServerFormData) => {
  const response = await request({
    url: `/api/internal/mcp_servers/${id}`,
    method: "PATCH",
    accept: "json",
    data: { mcp_server: data },
  });

  return response.json() as Promise<McpServerCreateUpdateResponse>;
};

export const deleteMcpServer = async (id: string) => {
  const response = await request({
    url: `/api/internal/mcp_servers/${id}`,
    method: "DELETE",
    accept: "json",
  });

  return response.json() as Promise<{ success: boolean }>;
};
