# frozen_string_literal: true

class McpServerPresenter
  include ProductsHelper
  include Rails.application.routes.url_helpers

  def initialize(seller:, mcp_server: nil)
    @seller = seller
    @mcp_server = mcp_server
  end

  def new_props
    {
      mcp_server: nil,
      errors: []
    }
  end

  def edit_props
    {
      mcp_server: mcp_server_data,
      errors: []
    }
  end

  def show_props
    {
      mcp_server: detailed_mcp_server_data
    }
  end

  private

  attr_reader :seller, :mcp_server

  def mcp_server_data
    return nil unless mcp_server

    config = mcp_server.mcp_server_config

    {
      id: mcp_server.unique_permalink,
      name: mcp_server.name,
      description: mcp_server.description,
      endpoint_url: config.endpoint_url,
      price_cents: mcp_server.price_cents,
      currency_code: seller.currency_code,
      health_status: config.health_status,
      last_health_check_at: config.last_health_check_at&.iso8601,
      is_active: config.is_active,
      created_at: mcp_server.created_at.iso8601,
      updated_at: mcp_server.updated_at.iso8601,
      supported_tools: config.supported_tools,
      api_documentation: config.api_documentation,
      server_metadata: config.server_metadata,
      published: mcp_server.published?
    }
  end

  def detailed_mcp_server_data
    return nil unless mcp_server

    config = mcp_server.mcp_server_config
    usage_stats = calculate_detailed_usage_stats

    {
      id: mcp_server.unique_permalink,
      name: mcp_server.name,
      description: mcp_server.description,
      endpoint_url: config.endpoint_url,
      price_cents: mcp_server.price_cents,
      currency_code: seller.currency_code,
      health_status: config.health_status,
      last_health_check_at: config.last_health_check_at&.iso8601,
      is_active: config.is_active,
      created_at: mcp_server.created_at.iso8601,
      updated_at: mcp_server.updated_at.iso8601,
      supported_tools: config.supported_tools,
      api_documentation: config.api_documentation,
      server_metadata: config.server_metadata,
      usage_stats: usage_stats,
      published: mcp_server.published?
    }
  end

  def calculate_detailed_usage_stats
    usages = mcp_server.mcp_server_usages.where(usage_date: 30.days.ago..Date.current)
    
    total_requests = usages.sum(:total_requests)
    successful_requests = usages.sum(:successful_requests)
    failed_requests = usages.sum(:failed_requests)
    
    # Calculate weighted average response time
    total_response_time = usages.sum("total_response_time_ms")
    average_response_time_ms = total_requests > 0 ? (total_response_time / total_requests).round(2) : 0.0
    
    success_rate = total_requests > 0 ? ((successful_requests.to_f / total_requests) * 100).round(2) : 0.0

    {
      total_requests: total_requests,
      successful_requests: successful_requests,
      failed_requests: failed_requests,
      average_response_time_ms: average_response_time_ms,
      success_rate: success_rate
    }
  end
end
