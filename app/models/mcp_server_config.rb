# frozen_string_literal: true

class McpServerConfig < ApplicationRecord
  include JsonData
  include TimestampScopes

  belongs_to :link, class_name: "Link"
  has_many :mcp_server_usages, foreign_key: "link_id", primary_key: "link_id", dependent: :destroy

  validates :endpoint_url, presence: true, format: { with: URI::DEFAULT_PARSER.make_regexp(%w[http https]) }
  validates :link_id, presence: true, uniqueness: true
  validates :health_status, inclusion: { in: %w[healthy unhealthy unknown] }

  attr_json_data_accessor :supported_tools, default: -> { [] }
  attr_json_data_accessor :server_metadata, default: -> { {} }

  scope :active, -> { where(is_active: true) }
  scope :inactive, -> { where(is_active: false) }
  scope :healthy, -> { where(health_status: "healthy") }
  scope :unhealthy, -> { where(health_status: "unhealthy") }
  scope :unknown_health, -> { where(health_status: "unknown") }

  before_validation :set_default_health_status, on: :create

  def healthy?
    health_status == "healthy"
  end

  def unhealthy?
    health_status == "unhealthy"
  end

  def unknown_health?
    health_status == "unknown"
  end

  def update_health_status!(status)
    update!(health_status: status, last_health_check_at: Time.current)
  end

  def needs_health_check?
    last_health_check_at.nil? || last_health_check_at < 5.minutes.ago
  end

  def as_json(options = {})
    super(options).merge(
      supported_tools: supported_tools,
      server_metadata: server_metadata
    )
  end

  private

  def set_default_health_status
    self.health_status ||= "unknown"
  end
end
