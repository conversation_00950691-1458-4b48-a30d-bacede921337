# frozen_string_literal: true

class McpServerUsage < ApplicationRecord
  include JsonData
  include TimestampScopes

  belongs_to :link, class_name: "<PERSON>"
  belongs_to :purchase, class_name: "Purchase"

  validates :link_id, presence: true
  validates :purchase_id, presence: true
  validates :api_key_hash, presence: true
  validates :usage_date, presence: true
  validates :api_calls_count, :total_requests, :successful_requests, :failed_requests,
            numericality: { greater_than_or_equal_to: 0 }
  validates :total_response_time_ms, :average_response_time_ms,
            numericality: { greater_than_or_equal_to: 0.0 }

  attr_json_data_accessor :usage_metadata, default: -> { {} }

  scope :for_date, ->(date) { where(usage_date: date) }
  scope :for_date_range, ->(start_date, end_date) { where(usage_date: start_date..end_date) }
  scope :for_api_key, ->(api_key_hash) { where(api_key_hash: api_key_hash) }
  scope :recent, -> { order(usage_date: :desc) }

  before_validation :set_usage_date, on: :create
  before_save :calculate_average_response_time

  def self.record_api_call(link:, purchase:, api_key_hash:, response_time_ms: 0, success: true, metadata: {})
    usage_date = Date.current
    usage = find_or_initialize_by(
      link: link,
      purchase: purchase,
      api_key_hash: api_key_hash,
      usage_date: usage_date
    )

    usage.api_calls_count += 1
    usage.total_requests += 1
    
    if success
      usage.successful_requests += 1
    else
      usage.failed_requests += 1
    end

    usage.total_response_time_ms += response_time_ms
    usage.usage_metadata = usage.usage_metadata.merge(metadata)
    
    usage.save!
    usage
  end

  def success_rate
    return 0.0 if total_requests.zero?
    (successful_requests.to_f / total_requests * 100).round(2)
  end

  def failure_rate
    return 0.0 if total_requests.zero?
    (failed_requests.to_f / total_requests * 100).round(2)
  end

  def as_json(options = {})
    super(options).merge(
      usage_metadata: usage_metadata,
      success_rate: success_rate,
      failure_rate: failure_rate
    )
  end

  private

  def set_usage_date
    self.usage_date ||= Date.current
  end

  def calculate_average_response_time
    if total_requests > 0 && total_response_time_ms > 0
      self.average_response_time_ms = (total_response_time_ms / total_requests).round(2)
    else
      self.average_response_time_ms = 0.0
    end
  end
end
